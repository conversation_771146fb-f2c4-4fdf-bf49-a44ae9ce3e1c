# AI 服务配置文件
# 您可以根据需要选择不同的 AI 服务

# 配置选项
AI_SERVICES = {
    "gemini": {
        "base_url": "https://generativelanguage.googleapis.com/v1beta/openai/",
        "api_key": "AIzaSyA9K-927zbbAjWsM_c4KQxofHQfQ4c6oMk",
        "models": ["gemini-1.5-flash", "gemini-1.5-flash-8b"]
    },
    "deepseek": {
        "base_url": "https://api.deepseek.com/v1",
        "api_key": "YOUR_DEEPSEEK_API_KEY",  # 请替换为您的 DeepSeek API Key
        "models": ["deepseek-chat"]
    },
    "openai": {
        "base_url": "https://api.openai.com/v1",
        "api_key": "YOUR_OPENAI_API_KEY",  # 请替换为您的 OpenAI API Key
        "models": ["gpt-3.5-turbo", "gpt-4"]
    }
}

# 当前使用的服务（可以修改为 "deepseek" 或 "openai"）
CURRENT_SERVICE = "gemini"

def get_current_config():
    """获取当前配置"""
    return AI_SERVICES[CURRENT_SERVICE]

def get_available_models():
    """获取当前服务可用的模型列表"""
    return AI_SERVICES[CURRENT_SERVICE]["models"]
