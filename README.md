# 股票分析程序使用说明

## 问题解决

### 1. CSV 文件错误修复
已修复 `analysis_history.csv` 文件为空导致的 "No columns to parse from file" 错误。
- 添加了异常处理机制
- 自动创建正确格式的历史记录文件

### 2. API 配额限制解决方案
针对 Gemini 2.5 Pro Preview 没有免费配额的问题，提供了以下解决方案：

#### 当前配置
- 默认使用 `gemini-1.5-flash` 模型（有免费配额）
- 添加了 `gemini-1.5-flash-8b` 作为备用模型
- 实现了自动重试机制

#### 备用 AI 服务
在 `ai_config.py` 文件中配置了多个 AI 服务选项：

1. **Gemini（当前使用）**
   - 模型：gemini-1.5-flash, gemini-1.5-flash-8b
   - 有免费配额

2. **DeepSeek**
   - 模型：deepseek-chat
   - 需要申请 API Key
   - 官网：https://platform.deepseek.com/

3. **OpenAI**
   - 模型：gpt-3.5-turbo, gpt-4
   - 需要付费 API Key
   - 官网：https://platform.openai.com/

#### 切换 AI 服务
要切换到其他 AI 服务，请：

1. 编辑 `ai_config.py` 文件
2. 修改 `CURRENT_SERVICE` 变量：
   ```python
   CURRENT_SERVICE = "deepseek"  # 或 "openai"
   ```
3. 填入对应的 API Key
4. 重启程序

### 3. 错误处理机制
程序现在包含完善的错误处理：
- 如果 AI 服务不可用，会自动生成基础分析报告
- 包含所有技术指标数据
- 确保程序不会因为 API 问题而崩溃

## 程序功能

1. **股票数据获取**：使用 akshare 获取实时股票数据
2. **技术指标计算**：MA5/10/20/60、RSI、MACD、布林带
3. **AI 分析**：智能分析股票走势和投资建议
4. **报告生成**：自动生成 Word 格式的分析报告
5. **历史记录**：保存和查看分析历史

## 使用方法

1. 启动程序：`python fenxi.py`
2. 在文本框中输入股票代码（如：000001）
3. 点击"分析股票"按钮
4. 等待分析完成，查看生成的 Word 报告

## 注意事项

- 确保网络连接正常
- 股票代码格式正确
- 如遇到 API 限制，程序会自动生成基础分析报告
- 投资有风险，分析结果仅供参考

## 依赖包

- tkinter（GUI界面）
- akshare（股票数据）
- pandas（数据处理）
- python-docx（Word文档）
- openai（AI接口）

## 故障排除

如果遇到问题：
1. 检查网络连接
2. 确认 API Key 有效
3. 查看控制台错误信息
4. 尝试切换不同的 AI 服务
